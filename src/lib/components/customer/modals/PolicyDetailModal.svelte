<!--
	@component PolicyDetailModal

	A comprehensive policy detail modal component that displays detailed information
	about an insurance policy including benefits, coverage, claims history, and contract conditions.

	This component works directly with raw policy details data from the TPA API workflow
	and provides a tabbed interface for viewing policy details and claims.

	@example
	```svelte
	<PolicyDetailModal
		bind:isOpen={policyDetailModalOpen}
		{selectedPolicyDetails}
		on:close={() => policyDetailModalOpen = false}
	/>
	```
-->
<script lang="ts">
	import { createEventDispatcher } from 'svelte';
	import { t } from '$lib/stores/i18n';
	import { formatTimestampDMY } from '$lib/utils';
	import { flyAndScale } from '$lib/utils';
	import {
		Button,
		Modal,
		Tabs,
		TabItem,
		Badge,
	} from 'flowbite-svelte';
	import {
		ClipboardListOutline,
		ExclamationCircleOutline,
		ClockOutline,
		BuildingOutline,
		CalendarMonthOutline,
		UserOutline,
		DollarOutline,
		InfoCircleOutline,
		NewspaperOutline,
	} from 'flowbite-svelte-icons';

	// Props
	/** Whether the modal is open */
	export let isOpen = false;
	/** Raw policy details data from the TPA API workflow */
	export let selectedPolicyDetails: any = null;

	// Event dispatcher
	const dispatch = createEventDispatcher<{
		close: void;
	}>();

	// Debug logging for props
	$: {
		console.log('PolicyDetailModal props changed:', {
			isOpen,
			hasSelectedPolicyDetails: !!selectedPolicyDetails,
			selectedPolicyDetails: selectedPolicyDetails,
			policyDetailsCount: selectedPolicyDetails?.step4_data?.ListOfPolDet?.length || 0,
			claimsCount: selectedPolicyDetails?.step4_data?.ListOfPolClaim?.length || 0
		});
	}

	// Modal transition parameters for smooth animations
	const modalTransitionParams = {
		y: -8,
		x: 0,
		start: 0.95,
		duration: 250
	};

	// Helper functions to extract data from raw API response
	function getClaims() {
		return selectedPolicyDetails?.step4_data?.ListOfPolClaim || [];
	}

	function getMemberInfo() {
		// Extract member info from the first claim or policy data
		const claims = getClaims();
		if (claims.length > 0) {
			return {
				name: claims[0].ClmMemberName || 'N/A',
				code: selectedPolicyDetails.member_code || 'N/A',
				policyNo: claims[0].ClmPolNo || 'N/A'
			};
		}
		return {
			name: 'N/A',
			code: selectedPolicyDetails?.member_code || 'N/A',
			policyNo: 'N/A'
		};
	}

	// Helper function to identify if a coverage item is a remark
	function isRemarkCoverage(coverage: any): boolean {
		if (!coverage.CovNo || coverage.CovNo.trim() === '') {
			return true;
		}
		return coverage.CovNo === 'Remarks' || coverage.CovNo === 'หมายเหตุ';
	}

	// Functions
	function closeModal() {
		console.log('PolicyDetailModal closing...');
		isOpen = false;
		dispatch('close');
		console.log('PolicyDetailModal close event dispatched');
	}
</script>

<!-- Policy Detail Modal -->
<Modal
	bind:open={isOpen}
	size="lg"
	autoclose={false}
	transition={flyAndScale}
	params={modalTransitionParams}
	class="h-[80vh] overflow-y-auto bg-gray-50"
>
	<div slot="header" class="flex flex-col gap-1 space-y-1">
		<div class="flex items-center justify-between">
			<h2 class="flex items-center gap-3 text-lg font-semibold">
				<ClipboardListOutline class="h-6 w-6" />
				{t('policy_modal_header_title')}
				<span
					class="rounded-full border border-green-200 bg-green-100 px-3 py-1 text-xs font-medium text-green-800"
				>
					{t('policy_status_active')}
				</span>
			</h2>
		</div>
		<!-- {#if selectedPolicyDetails}
			{@const memberInfo = getMemberInfo()}
			<div class="flex flex-col gap-1 text-sm text-gray-600 sm:flex-row sm:gap-4">
				<span
					><strong>
						{t('policy_modal_header_member_name')}
					</strong>
					{memberInfo.name}</span
				>
				<span
					><strong>
						{t('policy_modal_header_member_code')}
					</strong>
					{memberInfo.code}</span
				>
				<span
					><strong>
						{t('policy_modal_info_policy_no')}
					</strong>
					{memberInfo.policyNo}</span
				>
			</div>
		{/if} -->
	</div>

	{#if selectedPolicyDetails}
		{@const policyDetails = selectedPolicyDetails.step4_data.ListOfPolDet || []}
		{@const claims = getClaims()}
		{@const memberInfo = getMemberInfo()}

		<div>
			<!-- Tabbed Interface -->
			<Tabs tabStyle="pill" >
				<!-- Policy Details Tab -->
				<TabItem open title="รายละเอียดกรมธรรม์">
					<svelte:fragment slot="title">
						รายละเอียดกรมธรรม์
					</svelte:fragment>

					<div class="space-y-4">
						<!-- Policy Information Section -->
						<div class="modal-content-section rounded-lg border border-gray-200 bg-white p-4">
							<h3 class="mb-3 text-lg font-semibold text-gray-700">
								ข้อมูลกรมธรรม์
							</h3>
							<div class="grid grid-cols-1 gap-3 sm:grid-cols-2">
								<div class="flex flex-col space-y-1 sm:flex-row sm:justify-start sm:space-y-0 items-center text-sm">
									<span class="text-gray-600 w-28">เลขที่กรมธรรม์</span>
									<span class="font-semibold text-gray-900 ">{memberInfo.policyNo}</span>
								</div>
								<div class="flex flex-col space-y-1 sm:flex-row sm:justify-start sm:space-y-0 items-center text-sm">
									<span class="text-gray-600 w-28">รหัสสมาชิก</span>
									<span class="font-semibold text-gray-900">{memberInfo.code}</span>
								</div>
								{#if claims.length > 0}
									<div class="flex flex-col space-y-1 sm:flex-row sm:justify-start sm:space-y-0 items-center text-sm">
										<span class="text-gray-600 w-28">บริษัทประกัน</span>
										<span class="font-semibold text-gray-900">{claims[0].ClmInsurerTH}</span>
									</div>
									<div class="flex flex-col space-y-1 sm:flex-row sm:justify-start sm:space-y-0 items-center text-sm">
										<span class="text-gray-600 w-28">ประเภทบัตร</span>
										<span class="font-semibold text-gray-900">{claims[0].ClmCardType}</span>
									</div>
								{/if}
							</div>
						</div>

						<!-- Benefits Coverage Section -->
						{#if policyDetails.length > 0}
							<div class="modal-content-section rounded-lg border border-gray-200 bg-white p-4">
								<div class="flex items-center justify-between mb-3">
									<h3 class="text-lg font-semibold text-gray-700">
										ผลประโยชน์
									</h3>
									<!-- <span class="text-sm text-gray-600">(หน่วย: บาท)</span> -->
								</div>

								<div class="space-y-4">
									{#each policyDetails as benefit}
										<div class="rounded-lg border border-gray-200 p-4 shadow-sm">
											<h4 class="mb-3 text-md font-semibold text-gray-900">
												{benefit.MainBenefit || benefit.MainBenefitEN}
											</h4>

											{#if benefit.Coverage && benefit.Coverage.length > 0}
												{@const regularCoverage = benefit.Coverage.filter(c => c.CovDesc && c.CovDesc.trim() && !isRemarkCoverage(c))}
												{@const remarksCoverage = benefit.Coverage.filter(c => c.CovDesc && c.CovDesc.trim() && isRemarkCoverage(c))}

												<!-- Regular Coverage Items -->
												{#if regularCoverage.length > 0}
													<div class="space-y-2">
														{#each regularCoverage as coverage}
															<div class="flex flex-col gap-2 rounded-lg bg-gray-50 p-3 sm:flex-row sm:justify-between text-sm">
																<div class="flex-1">
																	<div class="text-gray-900">
																		{coverage.CovNo}. {coverage.CovDesc}
																	</div>
																	{#if coverage.CovDescEN && coverage.CovDescEN !== coverage.CovDesc}
																		<div class="text-sm text-gray-600">
																			{coverage.CovDescEN}
																		</div>
																	{/if}
																</div>
																{#if coverage.CovLimit && coverage.CovLimit.trim()}
																	<div class="text-right">
																		<div class="font-bold text-gray-600">
																			{coverage.CovLimit} บาท
																		</div>
																		{#if coverage.CovUtilized && coverage.CovUtilized.trim() && coverage.CovUtilized !== '-'}
																			<div class="text-xs text-gray-500">
																				ใช้สิทธิ์ไปแล้ว {coverage.CovUtilized}
																			</div>
																		{/if}
																	</div>
																{/if}
															</div>
														{/each}
													</div>
												{/if}

												<!-- Consolidated Remarks Section -->
												{#if remarksCoverage.length > 0}
													<div class="mt-4 rounded-lg border border-amber-200 bg-amber-50 p-4 text-sm">
														<div class="mb-3 flex items-center gap-2">
															<ExclamationCircleOutline class="h-5 w-5 text-amber-600" />
															<h5 class="font-semibold text-amber-800">หมายเหตุ</h5>
														</div>
														<div class="space-y-1">
															{#each remarksCoverage as remark}
																<div class="">
																	<div class="text-gray-900">
																		{remark.CovDesc}
																	</div>
																</div>
															{/each}
														</div>
													</div>
												{/if}
											{/if}
										</div>
									{/each}
								</div>
							</div>
						{/if}
					</div>
				</TabItem>

				<!-- Claims Tab -->
				<TabItem title="ประวัติการเคลม">
					<svelte:fragment slot="title">
						ประวัติการเคลม ({claims.length})
					</svelte:fragment>

					<div class="space-y-4">
						{#if claims.length === 0}
							<div class="flex flex-col items-center justify-center py-12 text-center">
								<div class="mb-4 rounded-full bg-gray-100 p-6">
									<ExclamationCircleOutline class="h-12 w-12 text-gray-400" />
								</div>
								<h3 class="mb-2 text-lg font-medium text-gray-900">ไม่มีประวัติการเคลม</h3>
								<p class="text-sm text-gray-500">ยังไม่มีการยื่นเคลมสำหรับกรมธรรม์นี้</p>
							</div>
						{:else}
							<!-- Claims Cards -->
							<div class="space-y-4">
								{#each claims as claim}
									<!-- Individual Claim Card -->
									<div class="modal-content-section rounded-lg border border-gray-200 bg-white p-6">
										<!-- Header Section -->
										<div class="mb-4 flex items-center justify-between">
											<div class="flex items-center gap-3">
												<h3 class="text-lg font-bold text-gray-900">
													เลขเคลม {claim.ClmNo}
												</h3>
											</div>
											<Badge
												rounded
												color={claim.ClmStatus === 'Approved' || claim.ClmStatus === 'Authorized' || claim.ClmStatus === 'Paid' || claim.ClmStatus === 'Closed'
													? 'green'
													: claim.ClmStatus === 'Rejected' || claim.ClmStatus === 'Denied'
														? 'red'
														: 'yellow'}
												class="{claim.ClmStatus === 'Approved' || claim.ClmStatus === 'Authorized' || claim.ClmStatus === 'Paid' || claim.ClmStatus === 'Closed'
													? 'bg-green-200 text-green-800'
													: claim.ClmStatus === 'Rejected' || claim.ClmStatus === 'Denied'
														? 'bg-red-200 text-red-800'
														: 'bg-yellow-200 text-yellow-800'} px-3 py-1 text-xs font-medium"
											>
												{claim.ClmStatusTxt || claim.ClmStatus}
											</Badge>
										</div>

										<!-- Program Information -->
										<!-- <div class="mb-6">
											<p class="text-gray-700 text-sm">
												<span class="font-medium">กรมธรรม์:</span>
												{claim.ClmPolNo}
											</p>
										</div> -->

										<!-- Main Information Grid -->
										<div class="mb-6 grid grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-4">
											<!-- Location -->
											<div class="flex items-start gap-3">
												<div class="rounded-lg bg-gray-100 p-2">
													<BuildingOutline class="h-5 w-5 text-gray-600" />
												</div>
												<div>
													<h4 class="font-semibold text-gray-900 text-sm">สถานพยาบาล</h4>
													<p class="text-gray-700 text-sm">{claim.ClmProviderTH}</p>
												</div>
											</div>

											<!-- Date -->
											<div class="flex items-start gap-3">
												<div class="rounded-lg bg-gray-100 p-2">
													<CalendarMonthOutline class="h-5 w-5 text-gray-600" />
												</div>
												<div>
													<h4 class="font-semibold text-gray-900 text-sm">วันที่รับบริการ</h4>
													<p class="text-gray-700 text-sm">{claim.ClmVisitDate}</p>
												</div>
											</div>

											<!-- Claim Type -->
											<div class="flex items-start gap-3">
												<div class="rounded-lg bg-gray-100 p-2">
													<UserOutline class="h-5 w-5 text-gray-600" />
												</div>
												<div>
													<h4 class="font-semibold text-gray-900 text-sm">ประเภทการเคลม</h4>
													<p class="text-gray-700 text-sm">{claim.ClmType}</p>
												</div>
											</div>

											<!-- Financial Details -->
											<div class="flex items-start gap-3">
												<div class="rounded-lg bg-gray-100 p-2">
													<DollarOutline class="h-5 w-5 text-gray-600" />
												</div>
												<div>
													<h4 class="font-semibold text-gray-900 text-sm">จำนวนเงิน</h4>
													<div class="space-y-1">
														<p class="text-sm text-gray-600">
															เรียกเก็บ
															<span class="font-bold text-gray-900">
																฿{claim.ClmIncurredAmt ? Number(claim.ClmIncurredAmt).toLocaleString() : ''}
															</span>
														</p>
														<p class="text-sm text-gray-600">
															จ่ายได้
															<span class="font-bold text-green-600">
																฿{claim.ClmPayable ? Number(claim.ClmPayable).toLocaleString() : ''}
															</span>
														</p>
														<p class="text-xs text-gray-500">
															แหล่งที่มา: {claim.ClmSource}
														</p>
													</div>
												</div>
											</div>
										</div>

										<!-- Analysis Section -->
										<div class="mb-6 flex items-start gap-3">
											<div class="rounded-lg bg-gray-100 p-2">
												<InfoCircleOutline class="h-5 w-5 text-gray-600" />
											</div>
											<div>
												<h4 class="font-semibold text-gray-900 text-sm">การวินิจฉัย</h4>
												<p class="text-gray-700 text-sm">
													{claim.ClmDiagTH}
												</p>
											</div>
										</div>

										<!-- Company Information -->
										<div class="flex items-center justify-between border-t border-gray-200 pt-4">
											<div class="flex items-center gap-3">
												<NewspaperOutline class="h-5 w-5 text-gray-600" />
												<span class="text-gray-700 text-sm">
													{claim.ClmCompanyTH}
												</span>
											</div>
											<Badge color="dark" class="bg-gray-200 text-gray-700 px-3 py-1">
												{claim.ClmCardType}
											</Badge>
										</div>
									</div>
								{/each}
							</div>
						{/if}
					</div>
				</TabItem>
			</Tabs>
		</div>
	{/if}

	<svelte:fragment slot="footer">
		<div class="modal-footer-right">
			<Button color="light" class="ml-auto" on:click={closeModal}>
				{t('policy_modal_close')}
			</Button>
		</div>
	</svelte:fragment>
</Modal>

<style>
	/* Modal content animations with accessibility support */
	:global(.modal-content-section) {
		animation: fadeInUp 0.3s ease-out;
		animation-fill-mode: both;
	}

	:global(.modal-content-section:nth-child(1)) {
		animation-delay: 0.05s;
	}
	:global(.modal-content-section:nth-child(2)) {
		animation-delay: 0.1s;
	}
	:global(.modal-content-section:nth-child(3)) {
		animation-delay: 0.15s;
	}
	:global(.modal-content-section:nth-child(4)) {
		animation-delay: 0.2s;
	}
	:global(.modal-content-section:nth-child(5)) {
		animation-delay: 0.25s;
	}
	:global(.modal-content-section:nth-child(6)) {
		animation-delay: 0.3s;
	}

	@keyframes fadeInUp {
		from {
			opacity: 0;
			transform: translateY(20px);
		}
		to {
			opacity: 1;
			transform: translateY(0);
		}
	}

	/* Respect user's motion preferences */
	@media (prefers-reduced-motion: reduce) {
		:global(.modal-content-section) {
			animation: none;
		}

		@keyframes fadeInUp {
			from,
			to {
				opacity: 1;
				transform: translateY(0);
			}
		}
	}

	/* Modal footer button alignment - target the specific modal footer */
	:global(.modal-footer-right) {
		display: flex !important;
		justify-content: flex-end !important;
		width: 100% !important;
	}

	/* Additional targeting for Flowbite Modal footer */
	:global([data-modal-target] .modal-footer-right),
	:global(.fixed .modal-footer-right) {
		display: flex !important;
		justify-content: flex-end !important;
		width: 100% !important;
		margin-left: auto !important;
	}
</style>